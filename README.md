# Computer Networks - Basic Course

Welcome to the Computer Networks Basic Course! This repository contains comprehensive materials covering fundamental networking concepts, devices, protocols, and security principles.

## Table of Contents

1. [Introduction to Computer Networks](#introduction-to-computer-networks)
2. [Networking Devices](#networking-devices)
3. [Network Protocols](#network-protocols)
4. [Network Security](#network-security)
5. [Getting Started](#getting-started)
6. [Resources](#resources)

---

## Introduction to Computer Networks

### What is a Computer Network?

A computer network is a collection of interconnected devices that can communicate and share resources with each other. Networks enable data exchange, resource sharing, and collaborative computing across different locations.

### Key Concepts

- **Nodes**: Individual devices in a network (computers, servers, printers, etc.)
- **Links**: Physical or wireless connections between nodes
- **Data Transmission**: The process of sending information from one node to another
- **Network Topology**: The physical or logical arrangement of network components

### Types of Networks

#### By Geographic Scope
- **PAN (Personal Area Network)**: Very small networks (1-10 meters)
- **LAN (Local Area Network)**: Networks within a building or campus
- **MAN (Metropolitan Area Network)**: Networks spanning a city
- **WAN (Wide Area Network)**: Networks covering large geographic areas

#### By Network Architecture
- **Client-Server**: Centralized model with dedicated servers
- **Peer-to-Peer (P2P)**: Decentralized model where all nodes are equal

### Network Topologies

- **Bus Topology**: All devices connected to a single cable
- **Star Topology**: All devices connected to a central hub
- **Ring Topology**: Devices connected in a circular fashion
- **Mesh Topology**: Multiple connections between devices
- **Hybrid Topology**: Combination of multiple topologies

---

## Networking Devices

### Layer 1 Devices (Physical Layer)

#### Repeaters
- **Function**: Amplify and regenerate signals
- **Use Case**: Extend network cable distances
- **Limitations**: Cannot filter or direct traffic

#### Hubs
- **Function**: Connect multiple devices in a star topology
- **Characteristics**: Operate at physical layer, create collision domains
- **Status**: Largely obsolete, replaced by switches

### Layer 2 Devices (Data Link Layer)

#### Switches
- **Function**: Forward frames based on MAC addresses
- **Features**:
  - MAC address learning
  - Collision domain separation
  - Full-duplex communication
  - VLAN support

#### Bridges
- **Function**: Connect two network segments
- **Purpose**: Reduce collision domains and filter traffic
- **Types**: Transparent bridges, source routing bridges

### Layer 3 Devices (Network Layer)

#### Routers
- **Function**: Forward packets between different networks
- **Key Features**:
  - IP address-based forwarding
  - Routing table maintenance
  - Network address translation (NAT)
  - Firewall capabilities

#### Layer 3 Switches
- **Function**: Combine switching and routing capabilities
- **Advantages**: High-speed inter-VLAN routing

### Other Important Devices

#### Access Points (APs)
- **Function**: Provide wireless network access
- **Types**: Standalone, controller-based, mesh

#### Firewalls
- **Function**: Control network traffic based on security rules
- **Types**: Packet filtering, stateful inspection, application layer

#### Load Balancers
- **Function**: Distribute network traffic across multiple servers
- **Benefits**: Improved performance and reliability

---

## Network Protocols

### OSI Model Overview

The OSI (Open Systems Interconnection) model provides a framework for understanding network communication through seven layers:

1. **Physical Layer**: Hardware transmission of raw bits
2. **Data Link Layer**: Node-to-node delivery and error detection
3. **Network Layer**: Routing and logical addressing
4. **Transport Layer**: End-to-end delivery and reliability
5. **Session Layer**: Session management
6. **Presentation Layer**: Data formatting and encryption
7. **Application Layer**: Network services to applications

### TCP/IP Protocol Suite

#### Internet Protocol (IP)
- **IPv4**: 32-bit addressing, most widely used
- **IPv6**: 128-bit addressing, next generation
- **Functions**: Routing, addressing, packet forwarding

#### Transmission Control Protocol (TCP)
- **Characteristics**: Connection-oriented, reliable, ordered delivery
- **Features**: Flow control, congestion control, error recovery
- **Use Cases**: Web browsing, email, file transfer

#### User Datagram Protocol (UDP)
- **Characteristics**: Connectionless, unreliable, fast
- **Features**: Low overhead, no connection establishment
- **Use Cases**: DNS, streaming media, online gaming

### Application Layer Protocols

#### HTTP/HTTPS
- **Purpose**: Web communication
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Features**: Stateless, request-response model

#### FTP/SFTP
- **Purpose**: File transfer
- **Ports**: 21 (FTP), 22 (SFTP)
- **Modes**: Active and passive

#### SMTP/POP3/IMAP
- **Purpose**: Email communication
- **SMTP**: Sending emails (Port 25, 587)
- **POP3**: Retrieving emails (Port 110)
- **IMAP**: Managing emails on server (Port 143)

#### DNS
- **Purpose**: Domain name resolution
- **Port**: 53
- **Types**: Recursive and iterative queries

#### DHCP
- **Purpose**: Automatic IP address assignment
- **Process**: DORA (Discover, Offer, Request, Acknowledge)

---

## Network Security

### Security Fundamentals

#### CIA Triad
- **Confidentiality**: Protecting data from unauthorized access
- **Integrity**: Ensuring data accuracy and completeness
- **Availability**: Ensuring systems are accessible when needed

### Common Security Threats

#### Network Attacks
- **DoS/DDoS**: Denial of Service attacks
- **Man-in-the-Middle**: Intercepting communications
- **Packet Sniffing**: Capturing network traffic
- **IP Spoofing**: Falsifying source IP addresses

#### Malware
- **Viruses**: Self-replicating malicious code
- **Worms**: Network-spreading malware
- **Trojans**: Disguised malicious software
- **Ransomware**: Data encryption for extortion

### Security Measures

#### Firewalls
- **Types**: Network, host-based, application
- **Rules**: Allow/deny based on various criteria
- **Deployment**: Perimeter, internal segmentation

#### Encryption
- **Symmetric**: Same key for encryption/decryption
- **Asymmetric**: Public/private key pairs
- **Protocols**: SSL/TLS, IPSec, WPA/WPA2

#### Access Control
- **Authentication**: Verifying user identity
- **Authorization**: Granting appropriate permissions
- **Accounting**: Logging and monitoring access

#### Network Monitoring
- **IDS**: Intrusion Detection Systems
- **IPS**: Intrusion Prevention Systems
- **SIEM**: Security Information and Event Management

---

## Getting Started

### Prerequisites
- Basic understanding of computer systems
- Familiarity with command-line interfaces
- Basic knowledge of operating systems

### Recommended Tools
- **Network Simulators**: Cisco Packet Tracer, GNS3
- **Protocol Analyzers**: Wireshark, tcpdump
- **Network Scanners**: Nmap, Nessus
- **Virtual Machines**: VirtualBox, VMware

### Learning Path
1. Start with basic networking concepts
2. Learn about different networking devices
3. Study protocol fundamentals
4. Practice with network simulation tools
5. Explore security concepts and implementations

---

## Resources

### Books
- "Computer Networking: A Top-Down Approach" by Kurose & Ross
- "Network+ Guide to Networks" by Tamara Dean
- "TCP/IP Illustrated" by W. Richard Stevens

### Online Resources
- Cisco Networking Academy
- CompTIA Network+ Study Materials
- RFC Documents (Internet Engineering Task Force)

### Certification Paths
- CompTIA Network+
- Cisco CCNA
- CompTIA Security+

---

## Contributing

Feel free to contribute to this repository by:
- Adding new examples and exercises
- Improving existing documentation
- Reporting issues or suggesting improvements

## License

This educational material is provided for learning purposes. Please respect copyright and licensing terms of referenced materials.
