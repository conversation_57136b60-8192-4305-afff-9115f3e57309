# Computer Networks - Basic Course

Welcome to the Computer Networks Basic Course! This repository contains comprehensive materials covering fundamental networking concepts, devices, protocols, and security principles with detailed flowcharts and enhanced explanations.

## Table of Contents

1. [Introduction to Computer Networks](#introduction-to-computer-networks)
2. [Networking Devices](#networking-devices)
3. [Network Protocols](#network-protocols)
4. [Network Security](#network-security)
5. [Network Communication Flow](#network-communication-flow)
6. [Troubleshooting Guide](#troubleshooting-guide)
7. [Practical Labs](#practical-labs)
8. [Getting Started](#getting-started)
9. [Resources](#resources)

---

## Introduction to Computer Networks

### What is a Computer Network?

A computer network is a collection of interconnected devices that can communicate and share resources with each other. Networks enable data exchange, resource sharing, and collaborative computing across different locations.

### Network Communication Flow

```mermaid
graph TD
    A[Source Device] --> B[Network Interface Card]
    B --> C[Physical Medium]
    C --> D[Network Infrastructure]
    D --> E[Routing/Switching]
    E --> F[Destination Network]
    F --> G[Destination Device]

    H[Data Encapsulation] --> I[Application Layer]
    I --> J[Transport Layer]
    J --> K[Network Layer]
    K --> L[Data Link Layer]
    L --> M[Physical Layer]
    M --> N[Transmission Medium]
```

### Key Concepts

- **Nodes**: Individual devices in a network (computers, servers, printers, routers, switches)
- **Links**: Physical or wireless connections between nodes (cables, radio waves, fiber optics)
- **Data Transmission**: The process of sending information from one node to another
- **Network Topology**: The physical or logical arrangement of network components
- **Bandwidth**: The maximum data transfer rate of a network connection
- **Latency**: The time delay in data transmission across a network
- **Throughput**: The actual amount of data successfully transmitted
- **Protocol**: Rules and standards governing network communication
- **Packet**: A unit of data transmitted over a network
- **Frame**: Data unit at the data link layer with header and trailer information

### Types of Networks

#### By Geographic Scope

```mermaid
graph LR
    A[PAN<br/>1-10m<br/>Bluetooth, USB] --> B[LAN<br/>Building/Campus<br/>Ethernet, WiFi]
    B --> C[MAN<br/>City-wide<br/>Fiber, Wireless]
    C --> D[WAN<br/>Global<br/>Internet, Satellite]
```

- **PAN (Personal Area Network)**:
  - Range: 1-10 meters
  - Examples: Bluetooth, USB connections
  - Devices: Smartphones, tablets, wearables

- **LAN (Local Area Network)**:
  - Range: Building or campus
  - Technologies: Ethernet, WiFi (802.11)
  - Speed: 10 Mbps to 10 Gbps
  - Examples: Office networks, home networks

- **MAN (Metropolitan Area Network)**:
  - Range: City or metropolitan area
  - Technologies: Fiber optic, wireless
  - Examples: City-wide WiFi, cable TV networks

- **WAN (Wide Area Network)**:
  - Range: Large geographic areas, global
  - Technologies: Internet, satellite, leased lines
  - Examples: Internet, corporate networks across countries

#### By Network Architecture

```mermaid
graph TD
    A[Network Architectures] --> B[Client-Server]
    A --> C[Peer-to-Peer]

    B --> D[Centralized Control]
    B --> E[Dedicated Servers]
    B --> F[Scalable]

    C --> G[Decentralized]
    C --> H[All Nodes Equal]
    C --> I[Resource Sharing]
```

- **Client-Server**:
  - Centralized model with dedicated servers
  - Advantages: Security, centralized management, scalability
  - Disadvantages: Single point of failure, higher cost

- **Peer-to-Peer (P2P)**:
  - Decentralized model where all nodes are equal
  - Advantages: Cost-effective, fault tolerance
  - Disadvantages: Security concerns, difficult management

### Network Topologies

```mermaid
graph TD
    A[Network Topologies] --> B[Physical Topologies]
    A --> C[Logical Topologies]

    B --> D[Bus]
    B --> E[Star]
    B --> F[Ring]
    B --> G[Mesh]
    B --> H[Hybrid]

    C --> I[Ethernet]
    C --> J[Token Ring]
    C --> K[FDDI]
```

#### Physical Topologies

- **Bus Topology**:
  - All devices connected to a single cable (backbone)
  - Advantages: Simple, cost-effective
  - Disadvantages: Single point of failure, collision domain

- **Star Topology**:
  - All devices connected to a central hub/switch
  - Advantages: Easy troubleshooting, no single point of failure for nodes
  - Disadvantages: Central device failure affects entire network

- **Ring Topology**:
  - Devices connected in a circular fashion
  - Advantages: Equal access, no collisions
  - Disadvantages: Single break affects entire network

- **Mesh Topology**:
  - Multiple connections between devices
  - Types: Full mesh (every device connected to every other), Partial mesh
  - Advantages: High redundancy, fault tolerance
  - Disadvantages: Complex, expensive

- **Hybrid Topology**:
  - Combination of multiple topologies
  - Examples: Star-bus, star-ring
  - Advantages: Flexible, scalable
  - Disadvantages: Complex design and management

---

## Networking Devices

### Device Classification by OSI Layer

```mermaid
graph TD
    A[OSI Layers & Devices] --> B[Layer 1 - Physical]
    A --> C[Layer 2 - Data Link]
    A --> D[Layer 3 - Network]
    A --> E[Layer 4-7 - Upper Layers]

    B --> F[Repeaters<br/>Hubs<br/>Cables]
    C --> G[Switches<br/>Bridges<br/>NICs]
    D --> H[Routers<br/>Layer 3 Switches<br/>Gateways]
    E --> I[Firewalls<br/>Load Balancers<br/>Proxies]
```

### Layer 1 Devices (Physical Layer)

#### Repeaters
- **Function**: Amplify and regenerate signals to extend transmission distance
- **Use Case**: Extend network cable distances beyond standard limits
- **Technical Details**:
  - Operate at bit level
  - No intelligence about data content
  - Simply boost signal strength
- **Limitations**: Cannot filter or direct traffic, propagate noise and collisions
- **Examples**: Ethernet repeaters, fiber optic repeaters

#### Hubs
- **Function**: Connect multiple devices in a star topology
- **Characteristics**:
  - Operate at physical layer
  - Create single collision domain
  - Half-duplex communication
  - Broadcast all data to all ports
- **Types**:
  - Passive hubs (no signal amplification)
  - Active hubs (signal amplification)
  - Intelligent hubs (basic management features)
- **Status**: Largely obsolete, replaced by switches

### Layer 2 Devices (Data Link Layer)

#### Switches

```mermaid
graph TD
    A[Switch Operations] --> B[Frame Reception]
    B --> C[MAC Address Learning]
    C --> D[MAC Table Lookup]
    D --> E{Destination Known?}
    E -->|Yes| F[Forward to Specific Port]
    E -->|No| G[Flood to All Ports]
    F --> H[Update MAC Table]
    G --> H
```

- **Function**: Forward frames based on MAC addresses
- **Key Features**:
  - MAC address learning and aging
  - Collision domain separation (each port = separate collision domain)
  - Full-duplex communication
  - VLAN support and trunking
  - Spanning Tree Protocol (STP) support
  - Quality of Service (QoS) capabilities
- **Types**:
  - Unmanaged switches (plug-and-play)
  - Managed switches (configurable features)
  - Smart/Web-managed switches (limited management)
- **Advanced Features**:
  - Port mirroring for monitoring
  - Link aggregation (LACP)
  - Power over Ethernet (PoE)
  - Storm control

#### Bridges
- **Function**: Connect two network segments and filter traffic
- **Purpose**: Reduce collision domains and improve network performance
- **Types**:
  - Transparent bridges (learn MAC addresses automatically)
  - Source routing bridges (used in Token Ring networks)
- **Operation**: Store and forward, learning bridge algorithm
- **Modern Usage**: Functionality integrated into switches

### Layer 3 Devices (Network Layer)

#### Routers

```mermaid
graph TD
    A[Router Packet Processing] --> B[Receive Packet]
    B --> C[Check Destination IP]
    C --> D[Routing Table Lookup]
    D --> E{Route Found?}
    E -->|Yes| F[Forward to Next Hop]
    E -->|No| G[Send ICMP Unreachable]
    F --> H[Decrement TTL]
    H --> I[Recalculate Checksum]
    I --> J[Forward Packet]
```

- **Function**: Forward packets between different networks based on IP addresses
- **Key Features**:
  - IP address-based forwarding
  - Routing table maintenance
  - Network address translation (NAT/PAT)
  - Access control lists (ACLs)
  - Dynamic routing protocol support
  - Quality of Service (QoS)
- **Routing Protocols**:
  - Static routing
  - RIP (Routing Information Protocol)
  - OSPF (Open Shortest Path First)
  - EIGRP (Enhanced Interior Gateway Routing Protocol)
  - BGP (Border Gateway Protocol)
- **Advanced Features**:
  - VPN support
  - Firewall capabilities
  - Load balancing
  - Traffic shaping

#### Layer 3 Switches
- **Function**: Combine switching and routing capabilities
- **Advantages**:
  - High-speed inter-VLAN routing
  - Wire-speed performance
  - Lower latency than traditional routers
- **Use Cases**: Campus networks, data centers

### Advanced Networking Devices

#### Access Points (APs)

```mermaid
graph TD
    A[Wireless Access Point] --> B[Radio Frequency Management]
    A --> C[Authentication & Security]
    A --> D[Bridge to Wired Network]

    B --> E[Channel Selection]
    B --> F[Power Control]
    B --> G[Interference Mitigation]

    C --> H[WPA/WPA2/WPA3]
    C --> I[802.1X Authentication]
    C --> J[MAC Filtering]
```

- **Function**: Provide wireless network access to devices
- **Types**:
  - Standalone/Autonomous APs
  - Controller-based APs (Lightweight)
  - Mesh APs
  - Outdoor APs
- **Standards**: 802.11a/b/g/n/ac/ax (WiFi 6)
- **Features**:
  - Multiple SSID support
  - VLAN tagging
  - Quality of Service (QoS)
  - Band steering
  - Load balancing

#### Firewalls
- **Function**: Control network traffic based on predetermined security rules
- **Types**:
  - Packet filtering firewalls
  - Stateful inspection firewalls
  - Application layer firewalls (proxy)
  - Next-generation firewalls (NGFW)
- **Deployment Models**:
  - Network-based firewalls
  - Host-based firewalls
  - Cloud-based firewalls
- **Advanced Features**:
  - Intrusion prevention (IPS)
  - Deep packet inspection (DPI)
  - Application awareness
  - User identity integration

#### Load Balancers
- **Function**: Distribute network traffic across multiple servers
- **Types**:
  - Layer 4 load balancers (transport layer)
  - Layer 7 load balancers (application layer)
- **Algorithms**:
  - Round robin
  - Least connections
  - Weighted round robin
  - IP hash
- **Benefits**:
  - Improved performance and reliability
  - High availability
  - Scalability
  - SSL termination

---

## Network Protocols

### OSI Model Overview

```mermaid
graph TD
    A[OSI 7-Layer Model] --> B[Layer 7 - Application<br/>HTTP, FTP, SMTP, DNS]
    A --> C[Layer 6 - Presentation<br/>SSL/TLS, JPEG, MPEG]
    A --> D[Layer 5 - Session<br/>NetBIOS, RPC, SQL]
    A --> E[Layer 4 - Transport<br/>TCP, UDP]
    A --> F[Layer 3 - Network<br/>IP, ICMP, OSPF, BGP]
    A --> G[Layer 2 - Data Link<br/>Ethernet, WiFi, PPP]
    A --> H[Layer 1 - Physical<br/>Cables, Hubs, Repeaters]
```

The OSI (Open Systems Interconnection) model provides a framework for understanding network communication through seven layers:

1. **Physical Layer**:
   - Hardware transmission of raw bits
   - Defines electrical, mechanical, and procedural specifications
   - Examples: Cables, connectors, voltage levels, data rates

2. **Data Link Layer**:
   - Node-to-node delivery and error detection
   - Frame synchronization, error control, flow control
   - Examples: Ethernet, WiFi (802.11), PPP

3. **Network Layer**:
   - Routing and logical addressing
   - Path determination, packet forwarding
   - Examples: IP, ICMP, OSPF, BGP

4. **Transport Layer**:
   - End-to-end delivery and reliability
   - Segmentation, flow control, error recovery
   - Examples: TCP, UDP

5. **Session Layer**:
   - Session management and control
   - Establishing, maintaining, terminating sessions
   - Examples: NetBIOS, RPC, SQL sessions

6. **Presentation Layer**:
   - Data formatting, encryption, compression
   - Translation between different data formats
   - Examples: SSL/TLS, JPEG, MPEG, ASCII

7. **Application Layer**:
   - Network services to applications
   - User interface and application-specific protocols
   - Examples: HTTP, FTP, SMTP, DNS

### TCP/IP Protocol Suite

```mermaid
graph TD
    A[TCP/IP Model] --> B[Application Layer<br/>HTTP, FTP, SMTP, DNS]
    A --> C[Transport Layer<br/>TCP, UDP]
    A --> D[Internet Layer<br/>IP, ICMP, ARP]
    A --> E[Network Access Layer<br/>Ethernet, WiFi]

    F[Data Encapsulation] --> G[Application Data]
    G --> H[TCP/UDP Header + Data]
    H --> I[IP Header + Segment]
    I --> J[Frame Header + Packet + Trailer]
```

#### Internet Protocol (IP)

**IPv4 (Internet Protocol version 4)**
- **Address Format**: 32-bit addressing (4 octets)
- **Address Space**: ~4.3 billion addresses
- **Header Size**: 20-60 bytes
- **Features**:
  - Fragmentation support
  - Time to Live (TTL)
  - Type of Service (ToS)
- **Address Classes**:
  - Class A: ******* to *************** (/8)
  - Class B: ********* to *************** (/16)
  - Class C: ********* to *************** (/24)
- **Private Address Ranges**:
  - 10.0.0.0/8
  - **********/12
  - ***********/16

**IPv6 (Internet Protocol version 6)**
- **Address Format**: 128-bit addressing (8 groups of 4 hexadecimal digits)
- **Address Space**: ~340 undecillion addresses
- **Header Size**: 40 bytes (fixed)
- **Improvements**:
  - Larger address space
  - Simplified header format
  - Built-in security (IPSec)
  - Better QoS support
  - Auto-configuration capabilities

#### Transmission Control Protocol (TCP)

```mermaid
sequenceDiagram
    participant Client
    participant Server

    Note over Client,Server: TCP Three-Way Handshake
    Client->>Server: SYN (seq=x)
    Server->>Client: SYN-ACK (seq=y, ack=x+1)
    Client->>Server: ACK (seq=x+1, ack=y+1)

    Note over Client,Server: Data Transfer
    Client->>Server: Data (seq=x+1)
    Server->>Client: ACK (ack=x+data_length+1)

    Note over Client,Server: Connection Termination
    Client->>Server: FIN (seq=x+n)
    Server->>Client: ACK (ack=x+n+1)
    Server->>Client: FIN (seq=y+m)
    Client->>Server: ACK (ack=y+m+1)
```

- **Characteristics**: Connection-oriented, reliable, ordered delivery
- **Key Features**:
  - Three-way handshake for connection establishment
  - Sequence numbers for ordered delivery
  - Acknowledgments for reliability
  - Flow control (sliding window)
  - Congestion control algorithms
  - Error detection and recovery
- **TCP Header Fields**:
  - Source/Destination ports
  - Sequence and acknowledgment numbers
  - Window size
  - Flags (SYN, ACK, FIN, RST, PSH, URG)
- **Use Cases**: Web browsing (HTTP/HTTPS), email (SMTP), file transfer (FTP)

#### User Datagram Protocol (UDP)

```mermaid
graph TD
    A[UDP Characteristics] --> B[Connectionless]
    A --> C[Unreliable]
    A --> D[Fast/Low Overhead]
    A --> E[No Flow Control]
    A --> F[No Error Recovery]

    G[UDP Header] --> H[Source Port - 16 bits]
    G --> I[Destination Port - 16 bits]
    G --> J[Length - 16 bits]
    G --> K[Checksum - 16 bits]
```

- **Characteristics**: Connectionless, unreliable, fast transmission
- **Features**:
  - No connection establishment
  - Minimal header overhead (8 bytes)
  - No flow control or congestion control
  - No guaranteed delivery or ordering
  - Best-effort delivery
- **Use Cases**:
  - DNS queries
  - Streaming media (video/audio)
  - Online gaming
  - DHCP
  - SNMP
  - Real-time applications where speed > reliability

### Application Layer Protocols

#### HTTP/HTTPS (HyperText Transfer Protocol)

```mermaid
sequenceDiagram
    participant Browser
    participant Web Server

    Browser->>Web Server: HTTP GET Request
    Note over Browser,Web Server: Request Headers:<br/>Host, User-Agent, Accept
    Web Server->>Browser: HTTP Response
    Note over Browser,Web Server: Response Headers:<br/>Content-Type, Content-Length<br/>Status Code: 200 OK
    Browser->>Web Server: Additional Requests (CSS, JS, Images)
    Web Server->>Browser: Additional Responses
```

- **Purpose**: Web communication between browsers and web servers
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Features**:
  - Stateless protocol
  - Request-response model
  - Support for various methods (GET, POST, PUT, DELETE, etc.)
  - Header-based metadata exchange
- **HTTP Methods**:
  - GET: Retrieve data
  - POST: Submit data
  - PUT: Update/create resource
  - DELETE: Remove resource
  - HEAD: Get headers only
- **Status Codes**:
  - 1xx: Informational
  - 2xx: Success (200 OK, 201 Created)
  - 3xx: Redirection (301 Moved, 302 Found)
  - 4xx: Client Error (404 Not Found, 403 Forbidden)
  - 5xx: Server Error (500 Internal Server Error)

#### FTP/SFTP (File Transfer Protocol)

```mermaid
graph TD
    A[FTP Connection] --> B[Control Connection<br/>Port 21]
    A --> C[Data Connection<br/>Port 20 or Random]

    D[FTP Modes] --> E[Active Mode<br/>Server initiates data connection]
    D --> F[Passive Mode<br/>Client initiates data connection]

    G[Secure Alternatives] --> H[SFTP<br/>SSH File Transfer Protocol]
    G --> I[FTPS<br/>FTP over SSL/TLS]
```

- **Purpose**: File transfer between client and server
- **Ports**: 21 (control), 20 (data) for FTP; 22 for SFTP
- **Modes**:
  - Active: Server initiates data connection to client
  - Passive: Client initiates both control and data connections
- **Security**:
  - FTP: Plain text (insecure)
  - SFTP: Encrypted over SSH
  - FTPS: FTP over SSL/TLS

#### Email Protocols (SMTP/POP3/IMAP)

```mermaid
graph TD
    A[Email Client] -->|SMTP<br/>Port 25/587| B[Mail Server]
    B -->|SMTP| C[Recipient Mail Server]
    C -->|POP3 Port 110<br/>or IMAP Port 143| D[Recipient Client]

    E[Email Flow] --> F[Compose Email]
    F --> G[Send via SMTP]
    G --> H[Store on Server]
    H --> I[Retrieve via POP3/IMAP]
```

- **SMTP (Simple Mail Transfer Protocol)**:
  - Purpose: Sending emails
  - Ports: 25 (standard), 587 (submission), 465 (SSL)
  - Process: Client to server, server to server

- **POP3 (Post Office Protocol v3)**:
  - Purpose: Retrieving emails from server
  - Port: 110 (995 for SSL)
  - Behavior: Download and delete from server

- **IMAP (Internet Message Access Protocol)**:
  - Purpose: Managing emails on server
  - Port: 143 (993 for SSL)
  - Behavior: Emails remain on server, synchronized across devices

#### DNS (Domain Name System)

```mermaid
graph TD
    A[DNS Query Process] --> B[Local DNS Cache]
    B -->|Cache Miss| C[Recursive DNS Server]
    C --> D[Root DNS Server]
    D --> E[TLD DNS Server<br/>.com, .org, etc.]
    E --> F[Authoritative DNS Server]
    F --> G[IP Address Response]
    G --> H[Cache and Return to Client]
```

- **Purpose**: Domain name to IP address resolution
- **Port**: 53 (UDP for queries, TCP for zone transfers)
- **Query Types**:
  - Recursive: DNS server handles entire resolution process
  - Iterative: Client follows referrals from DNS servers
- **Record Types**:
  - A: IPv4 address
  - AAAA: IPv6 address
  - CNAME: Canonical name (alias)
  - MX: Mail exchange
  - NS: Name server
  - PTR: Reverse lookup
  - TXT: Text records

#### DHCP (Dynamic Host Configuration Protocol)

```mermaid
sequenceDiagram
    participant Client
    participant DHCP Server

    Note over Client,DHCP Server: DORA Process
    Client->>DHCP Server: DHCP Discover (Broadcast)
    DHCP Server->>Client: DHCP Offer (IP + Config)
    Client->>DHCP Server: DHCP Request (Accept Offer)
    DHCP Server->>Client: DHCP Acknowledge (Confirm)

    Note over Client,DHCP Server: Lease Renewal
    Client->>DHCP Server: DHCP Request (Renew)
    DHCP Server->>Client: DHCP Acknowledge
```

- **Purpose**: Automatic IP address and network configuration assignment
- **Process**: DORA (Discover, Offer, Request, Acknowledge)
- **Configuration Parameters**:
  - IP address and subnet mask
  - Default gateway
  - DNS servers
  - Lease time
  - Domain name
- **Lease Management**:
  - Lease time: Duration of IP assignment
  - Renewal: At 50% of lease time
  - Rebinding: At 87.5% of lease time
  - Release: Manual or automatic at lease expiration

---

## Network Security

### Security Fundamentals

```mermaid
graph TD
    A[Network Security Principles] --> B[CIA Triad]
    B --> C[Confidentiality<br/>Data Privacy]
    B --> D[Integrity<br/>Data Accuracy]
    B --> E[Availability<br/>System Access]

    A --> F[Additional Principles]
    F --> G[Authentication<br/>Identity Verification]
    F --> H[Authorization<br/>Access Control]
    F --> I[Non-repudiation<br/>Action Accountability]
    F --> J[Accountability<br/>Audit Trails]
```

#### CIA Triad
- **Confidentiality**:
  - Protecting data from unauthorized access and disclosure
  - Methods: Encryption, access controls, data classification
  - Examples: SSL/TLS, VPNs, file permissions

- **Integrity**:
  - Ensuring data accuracy, completeness, and authenticity
  - Methods: Checksums, digital signatures, version control
  - Examples: Hash functions, digital certificates

- **Availability**:
  - Ensuring systems and data are accessible when needed
  - Methods: Redundancy, backup systems, disaster recovery
  - Examples: Load balancing, clustering, UPS systems

### Common Security Threats

#### Network Attacks

```mermaid
graph TD
    A[Network Attack Types] --> B[Passive Attacks]
    A --> C[Active Attacks]

    B --> D[Eavesdropping<br/>Packet Sniffing]
    B --> E[Traffic Analysis<br/>Pattern Recognition]

    C --> F[DoS/DDoS<br/>Service Disruption]
    C --> G[Man-in-the-Middle<br/>Communication Interception]
    C --> H[IP Spoofing<br/>Identity Falsification]
    C --> I[Session Hijacking<br/>Connection Takeover]
```

**Denial of Service (DoS/DDoS)**
- **Purpose**: Overwhelm target system to deny service to legitimate users
- **Types**:
  - Volume-based: Bandwidth consumption
  - Protocol-based: Exploit protocol weaknesses
  - Application-based: Target specific applications
- **DDoS**: Distributed attack using multiple compromised systems (botnet)
- **Mitigation**: Rate limiting, traffic filtering, CDNs

**Man-in-the-Middle (MITM)**
- **Method**: Attacker intercepts communication between two parties
- **Techniques**: ARP spoofing, DNS spoofing, rogue access points
- **Prevention**: Encryption, certificate validation, secure protocols

**Packet Sniffing**
- **Method**: Capturing and analyzing network traffic
- **Tools**: Wireshark, tcpdump, network taps
- **Protection**: Encryption, switched networks, network segmentation

**IP Spoofing**
- **Method**: Falsifying source IP address in packets
- **Uses**: Bypass access controls, launch attacks anonymously
- **Prevention**: Ingress/egress filtering, authentication

#### Malware Categories

```mermaid
graph TD
    A[Malware Types] --> B[Viruses<br/>Self-replicating<br/>Requires host file]
    A --> C[Worms<br/>Self-propagating<br/>Network spreading]
    A --> D[Trojans<br/>Disguised malware<br/>Appears legitimate]
    A --> E[Ransomware<br/>Data encryption<br/>Payment demand]
    A --> F[Spyware<br/>Information theft<br/>Covert monitoring]
    A --> G[Adware<br/>Unwanted advertisements<br/>Revenue generation]
    A --> H[Rootkits<br/>System-level access<br/>Stealth operation]
```

- **Viruses**:
  - Self-replicating malicious code that attaches to host files
  - Requires user action to spread
  - Types: File infectors, boot sector, macro viruses

- **Worms**:
  - Self-propagating malware that spreads across networks
  - No user interaction required
  - Examples: Code Red, Conficker, WannaCry

- **Trojans**:
  - Disguised malicious software appearing legitimate
  - Creates backdoors for remote access
  - Types: Remote access, data theft, banking trojans

- **Ransomware**:
  - Encrypts victim's data and demands payment
  - Growing threat to organizations
  - Prevention: Backups, user training, endpoint protection

### Security Measures

#### Firewalls

```mermaid
graph TD
    A[Firewall Types] --> B[Network Firewalls]
    A --> C[Host-based Firewalls]
    A --> D[Application Firewalls]

    B --> E[Packet Filtering<br/>Layer 3/4 inspection]
    B --> F[Stateful Inspection<br/>Connection tracking]
    B --> G[Next-Gen Firewalls<br/>Deep packet inspection]

    H[Firewall Rules] --> I[Source/Destination IP]
    H --> J[Port Numbers]
    H --> K[Protocol Type]
    H --> L[Application/Service]
    H --> M[Time-based Rules]
```

**Firewall Types**:
- **Network Firewalls**:
  - Protect entire network segments
  - Deployed at network perimeter or internal boundaries
  - Types: Packet filtering, stateful, proxy, NGFW

- **Host-based Firewalls**:
  - Protect individual devices
  - Software-based, OS-integrated
  - Examples: Windows Firewall, iptables

- **Application Firewalls**:
  - Protect specific applications (e.g., web applications)
  - Layer 7 inspection and filtering
  - Examples: Web Application Firewalls (WAF)

**Deployment Strategies**:
- Perimeter security (DMZ)
- Internal network segmentation
- Defense in depth
- Zero-trust architecture

#### Encryption

```mermaid
graph TD
    A[Encryption Types] --> B[Symmetric Encryption<br/>Same key for encrypt/decrypt]
    A --> C[Asymmetric Encryption<br/>Public/private key pairs]

    B --> D[AES<br/>Advanced Encryption Standard]
    B --> E[DES/3DES<br/>Data Encryption Standard]
    B --> F[ChaCha20<br/>Stream cipher]

    C --> G[RSA<br/>Rivest-Shamir-Adleman]
    C --> H[ECC<br/>Elliptic Curve Cryptography]
    C --> I[Diffie-Hellman<br/>Key exchange]

    J[Encryption Protocols] --> K[SSL/TLS<br/>Web security]
    J --> L[IPSec<br/>Network layer security]
    J --> M[WPA/WPA2/WPA3<br/>Wireless security]
```

**Symmetric Encryption**:
- Same key used for encryption and decryption
- Fast and efficient for large amounts of data
- Key distribution challenge
- Examples: AES-256, ChaCha20

**Asymmetric Encryption**:
- Public/private key pairs
- Slower but solves key distribution problem
- Used for key exchange and digital signatures
- Examples: RSA, ECC

**Hybrid Systems**:
- Combine symmetric and asymmetric encryption
- Asymmetric for key exchange, symmetric for data
- Examples: TLS handshake process

#### Access Control

```mermaid
graph TD
    A[Access Control Models] --> B[DAC<br/>Discretionary Access Control]
    A --> C[MAC<br/>Mandatory Access Control]
    A --> D[RBAC<br/>Role-Based Access Control]
    A --> E[ABAC<br/>Attribute-Based Access Control]

    F[AAA Framework] --> G[Authentication<br/>Who are you?]
    F --> H[Authorization<br/>What can you do?]
    F --> I[Accounting<br/>What did you do?]

    G --> J[Something you know<br/>Passwords, PINs]
    G --> K[Something you have<br/>Tokens, smart cards]
    G --> L[Something you are<br/>Biometrics]
```

**Authentication Methods**:
- **Single-factor**: Password only
- **Multi-factor (MFA)**: Combination of factors
- **Biometric**: Fingerprint, facial recognition
- **Token-based**: Hardware/software tokens
- **Certificate-based**: Digital certificates

**Authorization Models**:
- **Role-Based Access Control (RBAC)**: Permissions based on user roles
- **Attribute-Based Access Control (ABAC)**: Dynamic permissions based on attributes
- **Principle of Least Privilege**: Minimum necessary access

#### Network Monitoring and Detection

```mermaid
graph TD
    A[Security Monitoring] --> B[IDS<br/>Intrusion Detection System]
    A --> C[IPS<br/>Intrusion Prevention System]
    A --> D[SIEM<br/>Security Information Event Management]

    B --> E[Network-based IDS<br/>Monitor network traffic]
    B --> F[Host-based IDS<br/>Monitor system activities]

    C --> G[Inline deployment<br/>Real-time blocking]
    C --> H[Signature-based detection<br/>Known attack patterns]
    C --> I[Anomaly-based detection<br/>Behavioral analysis]

    D --> J[Log aggregation<br/>Centralized collection]
    D --> K[Correlation analysis<br/>Pattern identification]
    D --> L[Incident response<br/>Automated workflows]
```

**Intrusion Detection Systems (IDS)**:
- **Purpose**: Monitor and detect suspicious activities
- **Types**: Network-based (NIDS), Host-based (HIDS)
- **Detection Methods**: Signature-based, anomaly-based, hybrid
- **Deployment**: Passive monitoring, alerting only

**Intrusion Prevention Systems (IPS)**:
- **Purpose**: Detect and actively block threats
- **Deployment**: Inline with network traffic
- **Actions**: Block, drop, reset connections
- **Integration**: Often combined with firewalls

**Security Information and Event Management (SIEM)**:
- **Purpose**: Centralized security monitoring and analysis
- **Functions**: Log collection, correlation, alerting, reporting
- **Benefits**: Compliance, incident response, threat hunting
- **Examples**: Splunk, IBM QRadar, ArcSight

---

## Network Communication Flow

### Complete Data Transmission Process

```mermaid
sequenceDiagram
    participant App as Application
    participant OS as Operating System
    participant NIC as Network Interface
    participant Switch as Switch/Router
    participant Internet as Internet
    participant DestNIC as Dest. Network Interface
    participant DestOS as Dest. Operating System
    participant DestApp as Dest. Application

    Note over App,DestApp: Complete Network Communication Flow

    App->>OS: Application data
    Note over OS: Layer 4: Add TCP/UDP header
    Note over OS: Layer 3: Add IP header
    Note over OS: Layer 2: Add Ethernet header
    OS->>NIC: Frame transmission
    NIC->>Switch: Physical signal
    Switch->>Internet: Routing decision
    Internet->>DestNIC: Packet delivery
    DestNIC->>DestOS: Frame reception
    Note over DestOS: Layer 2: Remove Ethernet header
    Note over DestOS: Layer 3: Remove IP header
    Note over DestOS: Layer 4: Remove TCP/UDP header
    DestOS->>DestApp: Application data
```

### OSI Layer Data Processing

```mermaid
graph TD
    A[Application Data] --> B[Layer 7: Application<br/>Add application headers]
    B --> C[Layer 6: Presentation<br/>Encryption/Compression]
    C --> D[Layer 5: Session<br/>Session management]
    D --> E[Layer 4: Transport<br/>Add TCP/UDP header<br/>Segmentation]
    E --> F[Layer 3: Network<br/>Add IP header<br/>Routing]
    F --> G[Layer 2: Data Link<br/>Add Ethernet header/trailer<br/>Framing]
    G --> H[Layer 1: Physical<br/>Convert to electrical signals]
    H --> I[Physical Medium<br/>Cables, wireless, fiber]

    J[Receiving End] --> K[Layer 1: Physical<br/>Receive electrical signals]
    K --> L[Layer 2: Data Link<br/>Remove Ethernet header/trailer]
    L --> M[Layer 3: Network<br/>Remove IP header]
    M --> N[Layer 4: Transport<br/>Remove TCP/UDP header<br/>Reassembly]
    N --> O[Layer 5: Session<br/>Session management]
    O --> P[Layer 6: Presentation<br/>Decryption/Decompression]
    P --> Q[Layer 7: Application<br/>Process application data]
```

---

## Troubleshooting Guide

### Network Troubleshooting Methodology

```mermaid
graph TD
    A[Network Problem] --> B[Identify Symptoms]
    B --> C[Gather Information]
    C --> D[Consider Possibilities]
    D --> E[Create Action Plan]
    E --> F[Implement Solution]
    F --> G[Test Results]
    G --> H{Problem Solved?}
    H -->|Yes| I[Document Solution]
    H -->|No| J[Try Next Solution]
    J --> E
    I --> K[Monitor System]
```

### Common Network Issues and Solutions

#### Connectivity Issues

**Problem**: No network connectivity
```mermaid
graph TD
    A[No Connectivity] --> B[Check Physical Layer]
    B --> C{Cable Connected?}
    C -->|No| D[Connect Cable]
    C -->|Yes| E[Check Link Lights]
    E --> F{Link Light On?}
    F -->|No| G[Check Cable/Port]
    F -->|Yes| H[Check IP Configuration]
    H --> I{Valid IP Address?}
    I -->|No| J[Configure IP/DHCP]
    I -->|Yes| K[Check Gateway]
```

**Troubleshooting Steps**:
1. **Physical Layer**: Check cables, ports, link lights
2. **Data Link Layer**: Verify switch connectivity, VLAN configuration
3. **Network Layer**: Check IP configuration, routing tables
4. **Transport Layer**: Verify port accessibility, firewall rules
5. **Application Layer**: Test specific services and applications

#### Performance Issues

**Problem**: Slow network performance
- **Causes**: Bandwidth saturation, network congestion, faulty hardware
- **Tools**: Bandwidth monitors, packet analyzers, performance baselines
- **Solutions**: Traffic shaping, QoS implementation, hardware upgrades

#### DNS Resolution Issues

**Problem**: Cannot resolve domain names
- **Symptoms**: Can ping IP addresses but not domain names
- **Troubleshooting**: Check DNS server configuration, test with nslookup/dig
- **Solutions**: Configure correct DNS servers, clear DNS cache

### Network Troubleshooting Tools

```mermaid
graph TD
    A[Troubleshooting Tools] --> B[Command Line Tools]
    A --> C[GUI Tools]
    A --> D[Hardware Tools]

    B --> E[ping<br/>Connectivity testing]
    B --> F[traceroute/tracert<br/>Path tracing]
    B --> G[nslookup/dig<br/>DNS queries]
    B --> H[netstat<br/>Connection status]
    B --> I[arp<br/>MAC address table]

    C --> J[Wireshark<br/>Packet analysis]
    C --> K[Network scanners<br/>Port scanning]
    C --> L[Bandwidth monitors<br/>Traffic analysis]

    D --> M[Cable testers<br/>Physical layer]
    D --> N[Network analyzers<br/>Protocol analysis]
    D --> O[Multimeters<br/>Electrical testing]
```

---

## Practical Labs

### Lab 1: Basic Network Setup

**Objective**: Configure a basic network with multiple devices

**Equipment Needed**:
- 2-3 computers or virtual machines
- 1 switch or hub
- Ethernet cables
- Router (optional)

**Tasks**:
1. Connect devices to switch
2. Configure static IP addresses
3. Test connectivity with ping
4. Analyze traffic with Wireshark

**Expected Outcomes**:
- Understand basic network connectivity
- Learn IP address configuration
- Practice using network tools

### Lab 2: VLAN Configuration

**Objective**: Implement network segmentation using VLANs

**Equipment Needed**:
- Managed switch
- Multiple computers
- Network simulation software (Packet Tracer)

**Tasks**:
1. Create multiple VLANs
2. Assign ports to VLANs
3. Configure trunk ports
4. Test inter-VLAN communication

**Expected Outcomes**:
- Understand VLAN concepts
- Learn network segmentation
- Practice switch configuration

### Lab 3: Routing Configuration

**Objective**: Configure routing between different networks

**Equipment Needed**:
- Router or Layer 3 switch
- Multiple network segments
- Routing protocol configuration

**Tasks**:
1. Configure multiple network interfaces
2. Set up static routes
3. Implement dynamic routing (RIP/OSPF)
4. Test routing table updates

**Expected Outcomes**:
- Understand routing concepts
- Learn routing protocol configuration
- Practice network design

### Lab 4: Network Security Implementation

**Objective**: Implement basic network security measures

**Equipment Needed**:
- Firewall device or software
- Network monitoring tools
- Security testing tools

**Tasks**:
1. Configure firewall rules
2. Set up access control lists
3. Implement network monitoring
4. Conduct security testing

**Expected Outcomes**:
- Understand security principles
- Learn security tool configuration
- Practice threat detection

---

## Getting Started

### Prerequisites
- Basic understanding of computer systems
- Familiarity with command-line interfaces
- Basic knowledge of operating systems

### Recommended Tools
- **Network Simulators**: Cisco Packet Tracer, GNS3
- **Protocol Analyzers**: Wireshark, tcpdump
- **Network Scanners**: Nmap, Nessus
- **Virtual Machines**: VirtualBox, VMware

### Learning Path
1. Start with basic networking concepts
2. Learn about different networking devices
3. Study protocol fundamentals
4. Practice with network simulation tools
5. Explore security concepts and implementations

---

## Resources

### Books
- "Computer Networking: A Top-Down Approach" by Kurose & Ross
- "Network+ Guide to Networks" by Tamara Dean
- "TCP/IP Illustrated" by W. Richard Stevens

### Online Resources
- Cisco Networking Academy
- CompTIA Network+ Study Materials
- RFC Documents (Internet Engineering Task Force)

### Certification Paths
- CompTIA Network+
- Cisco CCNA
- CompTIA Security+

---

## Contributing

Feel free to contribute to this repository by:
- Adding new examples and exercises
- Improving existing documentation
- Reporting issues or suggesting improvements

## License

This educational material is provided for learning purposes. Please respect copyright and licensing terms of referenced materials.
